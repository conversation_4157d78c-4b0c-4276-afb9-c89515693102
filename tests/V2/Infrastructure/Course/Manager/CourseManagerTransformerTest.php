<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Course\Manager;

use App\Tests\V2\Mother\Course\Manager\CourseManagerMother;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Course\Manager\CourseManagerTransformer;
use PHPUnit\Framework\TestCase;

class CourseManagerTransformerTest extends TestCase
{
    public function testFromCollectionToArrayWithMultipleCourses(): void
    {
        $courseManager1 = CourseManagerMother::create(
            userId: new Id(123),
            courseId: new Id(1)
        );
        $courseManager2 = CourseManagerMother::create(
            userId: new Id(123),
            courseId: new Id(2)
        );
        $courseManager3 = CourseManagerMother::create(
            userId: new Id(123),
            courseId: new Id(15)
        );
        
        $collection = new CourseManagerCollection([$courseManager1, $courseManager2, $courseManager3]);
        
        $result = CourseManagerTransformer::fromCollectionToArray($collection);
        
        $expected = [
            ['id' => 1],
            ['id' => 2],
            ['id' => 15],
        ];
        
        $this->assertEquals($expected, $result);
        $this->assertCount(3, $result);
    }

    public function testFromCollectionToArrayWithEmptyCollection(): void
    {
        $collection = new CourseManagerCollection([]);
        
        $result = CourseManagerTransformer::fromCollectionToArray($collection);
        
        $this->assertEquals([], $result);
        $this->assertCount(0, $result);
    }

    public function testFromCollectionToArrayWithSingleCourse(): void
    {
        $courseManager = CourseManagerMother::create(
            userId: new Id(456),
            courseId: new Id(99)
        );
        
        $collection = new CourseManagerCollection([$courseManager]);
        
        $result = CourseManagerTransformer::fromCollectionToArray($collection);
        
        $expected = [
            ['id' => 99],
        ];
        
        $this->assertEquals($expected, $result);
        $this->assertCount(1, $result);
    }

    public function testFromCollectionToArrayStructure(): void
    {
        $courseManager = CourseManagerMother::create(
            userId: new Id(123),
            courseId: new Id(42)
        );
        
        $collection = new CourseManagerCollection([$courseManager]);
        
        $result = CourseManagerTransformer::fromCollectionToArray($collection);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey(0, $result);
        $this->assertArrayHasKey('id', $result[0]);
        $this->assertIsInt($result[0]['id']);
        $this->assertEquals(42, $result[0]['id']);
    }
}
