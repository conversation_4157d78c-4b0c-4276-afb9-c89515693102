<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Controller\Admin;

use App\Tests\V2\Mother\Course\Manager\CourseManagerMother;
use App\V2\Application\Query\Admin\GetManagerCourses;
use App\V2\Application\QueryHandler\Admin\GetManagerCoursesException;
use App\V2\Domain\Bus\QueryBus;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Infrastructure\Controller\Admin\GetManagerCoursesController;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetManagerCoursesControllerTest extends TestCase
{
    private QueryBus|MockObject $queryBus;
    private GetManagerCoursesController $controller;

    protected function setUp(): void
    {
        $this->queryBus = $this->createMock(QueryBus::class);
        $this->controller = new GetManagerCoursesController();
        
        // Use reflection to inject the query bus
        $reflection = new \ReflectionClass($this->controller);
        $property = $reflection->getProperty('queryBus');
        $property->setAccessible(true);
        $property->setValue($this->controller, $this->queryBus);
    }

    public function testInvokeSuccessWithCourses(): void
    {
        $userId = 123;
        $request = new Request();
        
        $courseManager1 = CourseManagerMother::create(
            userId: new Id($userId),
            courseId: new Id(1)
        );
        $courseManager2 = CourseManagerMother::create(
            userId: new Id($userId),
            courseId: new Id(2)
        );
        
        $collection = new CourseManagerCollection([$courseManager1, $courseManager2]);
        
        $this->queryBus
            ->expects($this->once())
            ->method('ask')
            ->with($this->callback(function (GetManagerCourses $query) use ($userId) {
                return $query->getUserId()->value() === $userId;
            }))
            ->willReturn($collection);
        
        $response = $this->controller->__invoke($request, $userId);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(2, $responseData['data']);
        $this->assertEquals(['id' => 1], $responseData['data'][0]);
        $this->assertEquals(['id' => 2], $responseData['data'][1]);
    }

    public function testInvokeSuccessWithEmptyCollection(): void
    {
        $userId = 123;
        $request = new Request();
        
        $emptyCollection = new CourseManagerCollection([]);
        
        $this->queryBus
            ->expects($this->once())
            ->method('ask')
            ->willReturn($emptyCollection);
        
        $response = $this->controller->__invoke($request, $userId);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(0, $responseData['data']);
    }

    public function testInvokeReturns404WhenUserNotFound(): void
    {
        $userId = 999;
        $request = new Request();
        
        $this->queryBus
            ->expects($this->once())
            ->method('ask')
            ->willThrowException(new UserNotFoundException());
        
        $response = $this->controller->__invoke($request, $userId);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('User not found', $responseData['error']);
    }

    public function testInvokeReturns422WhenUserIsNotManager(): void
    {
        $userId = 123;
        $request = new Request();
        
        $this->queryBus
            ->expects($this->once())
            ->method('ask')
            ->willThrowException(GetManagerCoursesException::userIsNotAManager());
        
        $response = $this->controller->__invoke($request, $userId);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('User is not a manager', $responseData['error']);
    }

    public function testInvokeThrowsValidatorExceptionForInvalidUserId(): void
    {
        $invalidUserId = 0; // Invalid ID (must be > 0)
        $request = new Request();
        
        $this->queryBus
            ->expects($this->never())
            ->method('ask');
        
        $this->expectException(ValidatorException::class);
        
        $this->controller->__invoke($request, $invalidUserId);
    }

    public function testInvokeThrowsValidatorExceptionForNegativeUserId(): void
    {
        $invalidUserId = -1; // Invalid ID (must be > 0)
        $request = new Request();
        
        $this->queryBus
            ->expects($this->never())
            ->method('ask');
        
        $this->expectException(ValidatorException::class);
        
        $this->controller->__invoke($request, $invalidUserId);
    }
}
