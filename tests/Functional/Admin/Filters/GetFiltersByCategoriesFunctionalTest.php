<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Filters;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCategoriesFiltersEndpoint;
use App\Tests\Functional\HelperTrait\FilterHelperTrait;
use PHPUnit\Framework\Attributes\DataProvider;

class GetFiltersByCategoriesFunctionalTest extends FunctionalTestCase
{
    use FilterHelperTrait;


    #[DataProvider('provideFiltersCategoriesOrder')]
    public function testItineraryOrder(array $categories, array $expected)
    {
        foreach ($categories as $key => $category) {
            $category = $this->createAndGetFilterCategory(name: $category['name'], sort: $category['sort'], is_ranking: $category['is_ranking']);
            $this->createAndGetFilter(name: "Example Filter {$key}", category: $category);
        }
        $userToken = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminCategoriesFiltersEndpoint::filterCategoriesEndpoint(),
            bearerToken: $userToken
        );
        $this->assertEquals(200, $response->getStatusCode());
        $data = $this->extractResponseData($response);
        $this->assertEquals($expected, $data);
    }

    public static function provideFiltersCategoriesOrder(): \Generator
    {
        yield 'One category One expected' => [
            'categories' => [['name' => 'Tienda (ES)', 'sort' => 1, 'is_ranking' => false]],
            'expected' => [
                [
                    'id' => 1,
                    'name' => 'Tienda (ES)',
                    'filters' => [['id' => 1, 'name' => 'Example Filter 0']],
                ],
            ],
        ];

        yield 'Multiple categories with different sort values' => [
            'categories' => [
                ['name' => 'Deparmento (ES)', 'sort' => 3, 'is_ranking' => true],
                ['name' => 'País (ES)', 'sort' => 2, 'is_ranking' => true],
                ['name' => 'Tienda (ES)', 'sort' => 1, 'is_ranking' => false],
            ],
            'expected' => [
                [
                    'id' => 3,
                    'name' => 'Tienda (ES)',
                    'filters' => [['id' => 3, 'name' => 'Example Filter 2']],
                ],
                [
                    'id' => 2,
                    'name' => 'País (ES)',
                    'filters' => [['id' => 2, 'name' => 'Example Filter 1']],
                ],
                [
                    'id' => 1,
                    'name' => 'Deparmento (ES)',
                    'filters' => [['id' => 1, 'name' => 'Example Filter 0']],
                ],
            ],
        ];
        yield 'Categories with same sort value' => [
            'categories' => [
                ['name' => 'País (ES)', 'sort' => 1, 'is_ranking' => true],
                ['name' => 'Tienda (ES)', 'sort' => 1, 'is_ranking' => false],
            ],
            'expected' => [
                [
                    'id' => 1,
                    'name' => 'País (ES)',
                    'filters' => [['id' => 1, 'name' => 'Example Filter 0']],
                ],
                [
                    'id' => 2,
                    'name' => 'Tienda (ES)',
                    'filters' => [['id' => 2, 'name' => 'Example Filter 1']],
                ],
            ],
        ];
        yield 'Categories with distint sorts' => [
            'categories' => [
                ['name' => 'Deparmento (ES)', 'sort' => 3, 'is_ranking' => true],
                ['name' => 'Tienda (ES)', 'sort' => 1, 'is_ranking' => false],
            ],
            'expected' => [
                [
                    'id' => 2,
                    'name' => 'Tienda (ES)',
                    'filters' => [['id' => 2, 'name' => 'Example Filter 1']],
                ],
                [
                    'id' => 1,
                    'name' => 'Deparmento (ES)',
                    'filters' => [['id' => 1, 'name' => 'Example Filter 0']],
                ],
            ],
        ];
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\Persistence\Mapping\MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([FilterCategory::class, Filter::class]);

        parent::tearDown();
    }
}
