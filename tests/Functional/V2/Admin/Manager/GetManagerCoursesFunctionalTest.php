<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Admin\Manager;

use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminManagerEndpoints;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetManagerCoursesFunctionalTest extends FunctionalTestCase
{
    private User $managerUser;
    private User $nonManagerUser;
    private Course $course1;
    private Course $course2;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a manager user
        $this->managerUser = $this->createUser([
            'email' => '<EMAIL>',
            'roles' => ['ROLE_USER', 'ROLE_MANAGER'],
            'firstName' => 'Manager',
            'lastName' => 'User',
        ]);

        // Create a non-manager user
        $this->nonManagerUser = $this->createUser([
            'email' => '<EMAIL>',
            'roles' => ['ROLE_USER'],
            'firstName' => 'Regular',
            'lastName' => 'User',
        ]);

        // Create courses
        $this->course1 = $this->createCourse(['name' => 'Test Course 1']);
        $this->course2 = $this->createCourse(['name' => 'Test Course 2']);

        // Assign manager to courses
        $this->assignManagerToCourse($this->managerUser, $this->course1);
        $this->assignManagerToCourse($this->managerUser, $this->course2);
    }

    public function testGetManagerCoursesSuccessWithCourses(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->getDefaultUser());

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($this->managerUser->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(2, $responseData['data']);

        // Check structure
        foreach ($responseData['data'] as $courseData) {
            $this->assertArrayHasKey('id', $courseData);
            $this->assertIsInt($courseData['id']);
        }

        // Check that the course IDs are present
        $courseIds = array_column($responseData['data'], 'id');
        $this->assertContains($this->course1->getId(), $courseIds);
        $this->assertContains($this->course2->getId(), $courseIds);
    }

    public function testGetManagerCoursesSuccessWithNoCourses(): void
    {
        // Create a manager with no courses
        $managerWithNoCourses = $this->createUser([
            'email' => '<EMAIL>',
            'roles' => ['ROLE_USER', 'ROLE_MANAGER'],
            'firstName' => 'Manager',
            'lastName' => 'NoCourses',
        ]);

        $userToken = $this->loginAndGetTokenForUser($this->getDefaultUser());

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($managerWithNoCourses->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(0, $responseData['data']);
        $this->assertEquals([], $responseData['data']);
    }

    public function testGetManagerCoursesRequiresAuthentication(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($this->managerUser->getId())
            // No bearer token
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testGetManagerCoursesReturns404WhenUserNotFound(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->getDefaultUser());
        $nonExistentUserId = 99999;

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($nonExistentUserId),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('User not found', $responseData['error']);
    }

    public function testGetManagerCoursesReturns422WhenUserIsNotManager(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->getDefaultUser());

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($this->nonManagerUser->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('User is not a manager', $responseData['error']);
    }

    #[DataProvider('invalidUserIdProvider')]
    public function testGetManagerCoursesValidationErrors(int $invalidUserId): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->getDefaultUser());

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($invalidUserId),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
    }

    public static function invalidUserIdProvider(): \Generator
    {
        yield 'zero user id' => [0];
        yield 'negative user id' => [-1];
        yield 'very negative user id' => [-999];
    }

    public function testGetManagerCoursesWithManagerEditor(): void
    {
        // Create a manager editor user
        $managerEditor = $this->createUser([
            'email' => '<EMAIL>',
            'roles' => ['ROLE_USER', 'ROLE_MANAGER_EDITOR'],
            'firstName' => 'Manager',
            'lastName' => 'Editor',
        ]);

        // Assign manager editor to a course
        $course = $this->createCourse(['name' => 'Editor Course']);
        $this->assignManagerToCourse($managerEditor, $course);

        $userToken = $this->loginAndGetTokenForUser($this->getDefaultUser());

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($managerEditor->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(1, $responseData['data']);
        $this->assertEquals($course->getId(), $responseData['data'][0]['id']);
    }

    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds([
            $this->managerUser->getId(),
            $this->nonManagerUser->getId(),
        ]);

        $this->hardDeleteCoursesByIds([
            $this->course1->getId(),
            $this->course2->getId(),
        ]);

        parent::tearDown();
    }

    private function assignManagerToCourse(User $manager, Course $course): void
    {
        $course->addManager($manager);
        $this->getEntityManager()->persist($course);
        $this->getEntityManager()->flush();
    }
}
