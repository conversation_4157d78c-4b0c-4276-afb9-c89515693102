<template>
  <div
    class="BasePagination"
    :class="{ disabled: disabled }"
  >
    <span
      class="chip"
      @click="setPage(pagination.getPrevPage())"
    >
      <Icon icon="angle-left" />
    </span>
    <span
      class="chip"
      :class="{ active: pagination.currentPage === 1 }"
      @click="setPage(1)"
    >
      1
    </span>
    <span
      v-for="page in pagesRange"
      :key="`chip_${page}`"
      class="chip"
      :class="{ active: pagination.currentPage === page }"
      @click="setPage(page)"
    >
      {{ page }}
    </span>
    <span
      class="chip"
      :class="{ active: pagination.currentPage === pagination.totalPages }"
      @click="setPage(pagination.totalPages)"
    >
      {{ pagination.totalPages }}
    </span>
    <span
      class="chip"
      @click="setPage(pagination.getNextPage())"
    >
      <Icon icon="angle-right" />
    </span>
  </div>
</template>

<script setup>
import { Pagination } from '@/contexts/shared/models/pagination.model.js'
import { computed } from 'vue'

const emit = defineEmits(['pageChange'])
const props = defineProps({
  pagination: { type: [Pagination, Object], default: () => ({}) },
  disabled: { type: Boolean, default: false },
})

const pagesRange = computed(() => {
  const center = Math.round(props.pagination.numberOfChips / 2) - 1
  const min = Math.min(
    Math.max(props.pagination.currentPage - center, 2),
    Math.max(props.pagination.totalPages - props.pagination.numberOfChips, 2)
  )
  const max = Math.min(min + props.pagination.numberOfChips, props.pagination.totalPages)

  const chips = []
  for (let i = min; i < max; i++) {
    chips.push(i)
  }
  return chips
})
function setPage(page = 1) {
  if (props.disabled || page === props.pagination.currentPage) {
    return null
  }
  props.pagination.setCurrentPage(page)
  emit('pageChange', page)
}
</script>

<style scoped lang="scss">
.BasePagination {
  display: flex;
  gap: 0.25rem;
  user-select: none;
  justify-content: flex-end;

  &.disabled {
    filter: grayscale(1);
  }

  .chip {
    display: grid;
    place-content: center;
    border-radius: 5px;
    padding: 0.15rem 0.25rem;
    height: 2rem;
    min-width: 2rem;
    font-size: 0.85rem;
    cursor: pointer;

    &:hover {
      background-color: var(--color-primary-lighter);
    }

    &.active {
      background-color: var(--color-primary);
      color: var(--color-neutral-lightest);
    }
  }
}
</style>
