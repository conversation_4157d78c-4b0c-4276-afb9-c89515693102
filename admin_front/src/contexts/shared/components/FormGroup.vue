<template>
  <div class="FormGroup">
    <header>
      <Icon
        class="icon"
        :icon="icon"
      />
      <p>{{ title }}</p>
    </header>
    <main><slot /></main>
  </div>
</template>

<script setup>
defineProps({
  title: { type: String, default: '' },
  icon: { type: [String, Array], default: '' },
})
</script>

<style scoped lang="scss">
.FormGroup {
  header {
    display: grid;
    grid-template-columns: 1.5rem auto;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.2rem;
    padding-bottom: 0.2rem;
    border: solid var(--input-border-color);
    border-width: 0 0 1px;

    .icon {
      margin: 0 auto;
    }

    p {
      font-weight: bold;
      margin: 0;
    }
  }
  main {
    margin-top: 1rem;
  }
}
</style>
