<template>
  <div class="NotFound">
    <span class="title">{{ $t(title) }}</span>
    <span>{{ $t(description) }}</span>
    <BaseButton
      v-if="routeName"
      @click="goToRoute"
    >
      <Icon :icon="buttonIcon" /> {{ $t(buttonText) }}
    </BaseButton>
  </div>
</template>

<script setup>
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const props = defineProps({
  title: { type: String, default: 'EMPTY.TITLE' },
  description: { type: String, default: 'EMPTY.DESCRIPTION' },
  buttonText: { type: String, default: 'SUBSCRIPTION.GO_BACK' },
  buttonIcon: { type: String, default: 'angle-left' },
  routeName: { type: String, default: '' },
})

function goToRoute() {
  if (!props.routeName) {
    return null
  }
  router.push({ name: props.routeName }).catch()
}
</script>

<style scoped lang="scss">
.NotFound {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
  background: url('@/contexts/shared/assets/images/empty_content.svg') no-repeat right;
  width: clamp(200px, 100%, 500px);
  margin: auto;
  height: 250px;

  .title {
    font-size: 1.5rem;
  }
  .BaseButton {
    margin-top: 1rem;
    margin-inline: auto;
    margin-bottom: -3rem;
  }
}
</style>
