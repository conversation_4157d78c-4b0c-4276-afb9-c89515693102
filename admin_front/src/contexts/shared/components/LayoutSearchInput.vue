<template>
  <Teleport
    defer
    to="#searchContainer"
  >
    <BaseInput
      v-model="innerValue"
      name="mainSearchInput"
      :icon="['fas', 'search']"
      :disabled="disabled"
      placeholder="Buscar"
    />
  </Teleport>
</template>

<script setup>
import BaseInput from '@/contexts/shared/components/BaseInput.vue'
import { computed, ref } from 'vue'
const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: { type: [String, Number], default: '' },
  disabled: { type: Boolean, default: false },
  debounceTime: { type: Number, default: 1200 },
})

const timeOutId = ref(null)
const innerValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    if (props.disabled) {
      return null
    }
    emitInputChange(value)
  },
})

function emitInputChange(value = '') {
  if (timeOutId.value) clearTimeout(timeOutId.value)
  timeOutId.value = setTimeout(() => emit('update:modelValue', value.trim()), props.debounceTime)
}
</script>
