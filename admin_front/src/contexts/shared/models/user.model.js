import { USER_ROLE_LIST } from '@/contexts/shared/constants/user.constants.js'

export class User {
  #permissionList = {}
  #roles = []
  #isSuperAdmin = false
  #isAdmin = false
  constructor({ roles = [], user = {} } = {}) {
    this.id = user.id || 0
    this.email = user.email || ''
    this.firstName = user.first_name || ''
    this.lastName = user.last_name || ''
    this.fullName = `${this.firstName} ${this.lastName}`.trim()
    this.code = user.code || ''
    this.locale = user.locale || 'en'
    this.localeCampus = user.locale_campus || 'en'
    this.timezone = user.timezone || ''
    const payloadRoles = roles || []
    this.#isAdmin = payloadRoles.some((role) => role === USER_ROLE_LIST.ADMIN)
    this.#isSuperAdmin = payloadRoles.some((role) => role === USER_ROLE_LIST.SUPER_ADMIN)
    this.#roles = payloadRoles.filter((role) => role !== USER_ROLE_LIST.ADMIN && role !== USER_ROLE_LIST.SUPER_ADMIN)
  }

  setPermissionList(permissionObj = {}) {
    this.#permissionList = permissionObj
  }

  hasFullAccess() {
    return !!this.#isSuperAdmin
  }

  isGranted(action = '') {
    if (this.#isSuperAdmin || this.#isAdmin) {
      return true
    }
    return this.#roles.some((role) => (this.#permissionList[role] || []).some((item) => item === action))
  }
}
