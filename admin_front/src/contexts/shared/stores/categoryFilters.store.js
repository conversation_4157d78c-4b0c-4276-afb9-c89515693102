import { defineStore } from 'pinia'

export const useCategoryFilters = defineStore('categoryFilters', () => {
  function loadFilters(url = '') {}
  function loadCategories(url = '') {}
  function addFilter(payload, url = '') {}
  function addAllFilters(payload, url = '') {}
  function removeFilter(payload, url = '') {}
  function removeAllFilters(payload, url = '') {}

  return {
    loadCategories,
    loadFilters,
    addFilter,
    addAllFilters,
    removeFilter,
    removeAllFilters,
  }
})
