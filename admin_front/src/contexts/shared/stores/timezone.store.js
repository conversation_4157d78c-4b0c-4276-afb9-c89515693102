import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import ApiService from '@/core/services/api.service.js'
import { LISTS_API_ROUTES } from '@/contexts/shared/constants/lists.constants.js'
import SelectOptionModel from '@/contexts/shared/models/selectOption.model.js'

export const useTimeZoneListStore = defineStore('timeZoneListStore', () => {
  const timeZoneList = ref([])
  async function loadTimeZoneList() {
    const { error, data } = await ApiService.get(LISTS_API_ROUTES.TIMEZONES)
    timeZoneList.value = (error ? [] : data || []).map((timezone) => `${timezone || ''}`.trim())
  }

  const timeZoneOptions = computed(() =>
    timeZoneList.value.map((timezone) => new SelectOptionModel({ label: timezone, value: timezone }))
  )

  return {
    timeZoneList,
    timeZoneOptions,
    loadTimeZoneList,
  }
})
