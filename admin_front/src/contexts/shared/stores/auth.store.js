import { defineStore } from 'pinia'
import { toast } from 'vue3-toastify'
import { REFRESH_TOKEN_URL } from '@/core/constants/general.constant.js'
import StorageService from '@/core/services/storage.service.js'
import ApiService from '@/core/services/api.service.js'
import { ref } from 'vue'

export const useAuthStore = defineStore('authStore', () => {
  const isAuth = ref(!!StorageService.getToken())
  async function login(payload = {}) {
    const { data, error } = await ApiService.post('/login', payload)
    if (error || !data?.token) {
      return toast.error(`${error?.message || 'Mising token'}`)
    }
    isAuth.value = true
    StorageService.setToken(data.token)
  }
  async function refreshToken(config = {}) {
    const { token, error } = await ApiService.post(REFRESH_TOKEN_URL, { refresh_token: refreshToken }, config)
    if (error || !token) {
      return toast.error(`${error?.message || 'Mising token'}`)
    }
    StorageService.setToken(token)
  }
  async function logout() {
    const { error } = await ApiService.get('/logout')
    if (error) {
      return toast.error(error.message)
    }

    StorageService.setToken()
    isAuth.value = false
  }

  return {
    isAuth,
    login,
    logout,
    refreshToken,
  }
})
