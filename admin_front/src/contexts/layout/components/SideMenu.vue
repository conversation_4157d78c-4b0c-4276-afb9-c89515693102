<template>
  <div
    v-on-click-outside="handlingClickOutside"
    class="SideMenu"
    :class="{ open: openModal }"
  >
    <div class="sideMenuContent">
      <span class="hideMin"><Icon icon="circle-info" /> 2.0.0</span>
      <MenuItem
        v-for="item in availableItems"
        :key="item.key"
        :item="item"
        :open="item.id === currentClickedItem"
        @toggle-open="handlingMenuOpen"
      />
      <Teleport
        defer
        to="#menuIcon"
      >
        <Icon
          icon="bars"
          @click="openModal = true"
        />
      </Teleport>
    </div>
    <div
      class="backdrop"
      @click="openModal = false"
    />
  </div>
</template>

<script setup>
import { LAYOUT_MENU_ITEMS } from '@/contexts/layout/constants/general_layout.constants.js'
import MenuItem from '@/contexts/layout/components/MenuItem.vue'
import { computed, inject, ref } from 'vue'
import {
  LAYOUT_PERMISSION_LIST,
  LAYOUT_PERMISSION_PREFIX,
} from '@/contexts/layout/constants/layoutPermission.constants.js'

const currentClickedItem = ref(-1)
function handlingMenuOpen(id) {
  if (id === currentClickedItem.value) currentClickedItem.value = -1
  else currentClickedItem.value = id
}
const openModal = ref(false)
function handlingClickOutside() {
  if (currentClickedItem.value > -1) currentClickedItem.value = -1
}

const user = inject('user')
const availableItems = computed(() => {
  if (user.hasFullAccess()) {
    return LAYOUT_MENU_ITEMS
  }

  return LAYOUT_MENU_ITEMS.map((item) => {
    const itemID = `${LAYOUT_PERMISSION_PREFIX}${item.id}`
    if (itemID === LAYOUT_PERMISSION_LIST.SUPER_ADMIN) {
      return false
    }
    if (user.isGranted(itemID) || itemID === LAYOUT_PERMISSION_LIST.GO_TO_CAMPUS) {
      return item
    }

    const newItem = item
    newItem.subItems = item.subItems.filter((subItem) => user.isGranted(`${LAYOUT_PERMISSION_PREFIX}${subItem.id}`))
    if (!newItem.subItems.length) {
      return false
    }
    return newItem
  }).filter(Boolean)
})
</script>

<style scoped lang="scss">
@use '@/contexts/shared/assets/styles/_breakpoints' as breakpoint;
.SideMenu {
  height: fit-content;
  max-width: 240px;

  .sideMenuContent {
    height: fit-content;
    background-color: var(--color-neutral-dark);
    color: var(--color-neutral-mid-light);
    border-radius: 7px 0 0 7px;

    span {
      padding: 0.5rem 0.7rem 0.5rem;
      font-size: 0.9rem;
      display: flex;
      gap: 0.75rem;
      align-items: center;
      color: var(--color-neutral-mid-dark);
    }
  }

  .backdrop {
    content: ' ';
    position: fixed;
    background-color: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(3px);
    inset: 0;
    z-index: 1;
    display: none;
  }

  @media #{breakpoint.$breakpoint-xl} {
    width: fit-content;
    .sideMenuContent {
      .hideMin {
        display: none;
      }
    }
  }

  @media #{breakpoint.$breakpoint-sm} {
    .sideMenuContent {
      position: fixed;
      top: 0;
      left: 0;
      height: 100svh;
      overflow-y: auto;
      z-index: 2;
      border-radius: 0;
      display: none;

      .hideMin {
        display: initial;
      }
    }

    &.open {
      .sideMenuContent,
      .backdrop {
        display: initial;
      }
    }
  }
}
</style>
